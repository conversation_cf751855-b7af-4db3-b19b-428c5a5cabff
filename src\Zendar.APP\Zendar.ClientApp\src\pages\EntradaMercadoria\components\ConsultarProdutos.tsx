import {
  ModalContent,
  ModalBody,
  useDisclosure,
  ModalHeader,
  Button,
  GridItem,
  Flex,
  Icon,
  Text,
  ModalProps as ModalChakraProps,
  FormLabel,
} from '@chakra-ui/react';
import { FormProvider } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';

import ConsultaProdutosPdvProvider, {
  ConsultaProdutoPdvContext,
  Produto,
} from 'store/PDV/ConsultaProdutoPdv';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalListarSaldoVariacoes from 'components/Modal/ModalListarSaldoVariacoes';
import Input from 'components/PDV/Input';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import { SelectCategoria } from 'components/Select/SelectCategoria';
import { SelectMulti } from 'components/Select/SelectMultiCheckbox';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { ConsultaProdutosTabela } from 'components/v2/ConsultaProdutos/ConsultaProdutosTabela';
import { ConsultaVariacoes } from 'components/v2/ConsultaProdutos/ConsultaVariacoes';
import { DrawerConsultaProdutos } from 'components/v2/ConsultaProdutos/Drawer';
import { useConsultaProdutos } from 'components/v2/ConsultaProdutos/hooks/useConsultaProdutos';
import { ModalFooterConsulta } from 'components/v2/ConsultaProdutos/ModalFooter';

import TipoFiltroProdutoEstoqueEnum from 'constants/enum/tipoFiltroProdutoEstoque';
import { BuscaIcon } from 'icons';

type ModalProps = Omit<ModalChakraProps, 'children' | 'isOpen' | 'onClose'> &
  InstanceProps<Produto> & {
    casasDecimais: {
      casasDecimaisQuantidade: number;
    };
    exibirBotaoAdicionarProduto?: boolean;
  };

export const ModalConsultaProdutosEntradaMercadoria = create<
  ModalProps,
  Produto
>(
  ({
    onResolve,
    onReject,
    casasDecimais,
    exibirBotaoAdicionarProduto = true,
    ...rest
  }) => {
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const {
      formMethods,
      isLargerThan1200,
      fecharModal,
      isOpenDrawer,
      onOpenDrawer,
      onCloseDrawer,
      tela,
      produtoSelecionado,
      setModalVisualizarEstoqueEstaAberto,
      setProdutoSelecionado,
      lancarProdutoSimples,
      produtos,
      paginationHandle,
      planosQuePossuemLocalEstoque,
      planoContratado,
      totalRegistros,
      escolherVariacao,
      paginationHandleGrade,
      totalRegistrosGrade,
      listarVariacoes,
      selecionarVariacao,
      variacaoSelecionada,
      lancarProdutoDesabilitado,
      isLargerThan900,
      setTela,
      setVariacaoSelecionada,
      modalVisualizarEstoqueEstaAberto,
      drawerFormMethods,
      handleLimparPesquisa,
      handleSubmit,
      marcas,
      isLoadingGradeVariacoes,
      isLoadingPesquisa,
    } = useConsultaProdutos({
      onClose,
      onReject,
      onResolve,
    });

    return (
      <ModalPadraoChakra
        isCentered={false}
        size="full"
        appendToParentPortal={false}
        {...rest}
        isOpen={isOpen}
        onClose={() => {
          fecharModal();
        }}
      >
        <ConsultaProdutosPdvProvider
          formMethods={formMethods}
          key="ConsultaProdutosPdvProvider"
          contextFiltros={undefined}
        >
          <ConsultaProdutoPdvContext.Consumer>
            {({ isLoading }) => {
              return (
                <ModalContent
                  h="unset"
                  bg="gray.50"
                  borderRadius="0px"
                  transition="right 0.5s ease-in-out, width 0.5s ease-in-out"
                  w={
                    isLargerThan1200 && isOpenDrawer && !isLoading
                      ? 'calc(100% - 502px)'
                      : '100%'
                  } // 502px é a largura do drawer
                  right={
                    isLargerThan1200 && isOpenDrawer && !isLoading
                      ? '255px'
                      : '0'
                  }
                  autoFocus={false}
                >
                  {(isLoading ||
                    isLoadingPesquisa ||
                    isLoadingGradeVariacoes) && <LoadingPadrao />}
                  <ModalHeader
                    px="32px"
                    py="20px"
                    color="violet.500"
                    fontWeight="normal"
                    fontSize="18px"
                  >
                    <Flex align="center" w="full" height="32px">
                      <Text>Consulta de produtos</Text>
                      {(!isOpenDrawer || !isLargerThan1200) && (
                        <Button
                          margin="0 0 0 auto"
                          colorScheme="gray"
                          variant="outlineDefault"
                          borderRadius="full"
                          w="120px"
                          height="32px"
                          transition="all 300ms"
                          _hover={{
                            '& svg': {
                              color: 'white',
                            },
                            bg: 'gray.500',
                            color: 'white',
                          }}
                          borderColor="gray.200"
                          onClick={
                            isLargerThan1200 || !isOpenDrawer
                              ? onOpenDrawer
                              : onCloseDrawer
                          }
                          leftIcon={
                            <Icon
                              as={BuscaIcon}
                              fontSize="12px"
                              color="black"
                              transition="all 300ms"
                            />
                          }
                        >
                          Filtros
                        </Button>
                      )}
                    </Flex>
                  </ModalHeader>
                  <ModalBody px="32px" pt="0px">
                    <SimpleGridForm
                      gap={{ base: '10px', sm: '10px', md: 8 }}
                      mb="60px"
                    >
                      <FormProvider {...formMethods}>
                        <GridItem mt={['10px', '10px', '0px']} colSpan={12}>
                          <ConsultaProdutosTabela
                            tela={tela}
                            produtoSelecionado={produtoSelecionado}
                            casasDecimais={casasDecimais}
                            isOpenDrawer={isOpenDrawer}
                            setModalVisualizarEstoqueEstaAberto={
                              setModalVisualizarEstoqueEstaAberto
                            }
                            setProdutoSelecionado={setProdutoSelecionado}
                            onCloseDrawer={onCloseDrawer}
                            lancarProdutoSimples={lancarProdutoSimples}
                            produtos={produtos}
                            paginationHandle={paginationHandle}
                            exibirConsultaEstoque={planosQuePossuemLocalEstoque.includes(
                              planoContratado
                            )}
                            totalRegistros={totalRegistros}
                            handleCliqueNoProdutoItem={(e, produto) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setProdutoSelecionado(produto);
                            }}
                            handleCliqueDuploNoProdutoItem={(produto) => {
                              if (!produto) {
                                return;
                              }

                              lancarProdutoSimples();
                            }}
                            handleCliqueNoEscolherVariacao={(e, produto) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setProdutoSelecionado(produto);
                              escolherVariacao(produto.id);
                            }}
                            handleCliqueDuploNoEscolherVariacao={(
                              e,
                              produto
                            ) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setProdutoSelecionado(produto);
                              escolherVariacao(produto.id);
                            }}
                          />
                          <ConsultaVariacoes
                            tela={tela}
                            paginationHandleGrade={paginationHandleGrade}
                            totalRegistrosGrade={totalRegistrosGrade}
                            listarVariacoes={listarVariacoes}
                            casasDecimais={casasDecimais}
                            toggleSelectVariacao={selecionarVariacao}
                            variacaoSelecionada={variacaoSelecionada}
                            handleDoubleClickVariacao={() => {}}
                          />
                        </GridItem>
                      </FormProvider>
                    </SimpleGridForm>
                  </ModalBody>
                  <ModalFooterConsulta
                    tela={tela}
                    produtoSelecionado={produtoSelecionado}
                    exibirBotaoSelecionarVariacao={false}
                    exibirBotaoAdicionarProduto={exibirBotaoAdicionarProduto}
                    lancarProdutoDesabilitado={lancarProdutoDesabilitado}
                    escolherVariacao={escolherVariacao}
                    lancarProdutoSimples={lancarProdutoSimples}
                    lancarProdutoVariacao={() => {}}
                    isLargerThan1200={isLargerThan1200}
                    isOpenDrawer={isOpenDrawer}
                    isLoading={isLoading}
                    isLargerThan900={isLargerThan900}
                    setProdutoSelecionado={setProdutoSelecionado}
                    setTela={setTela}
                    setVariacaoSelecionada={setVariacaoSelecionada}
                    variacaoSelecionada={variacaoSelecionada}
                    onClose={onClose}
                    onReject={onReject}
                  />
                  <ModalListarSaldoVariacoes
                    isOpen={
                      modalVisualizarEstoqueEstaAberto && !!produtoSelecionado
                    }
                    setIsOpen={setModalVisualizarEstoqueEstaAberto}
                    idProduto={produtoSelecionado?.id || ''}
                  />
                  <DrawerConsultaProdutos
                    key="drawerFiltrosConsultaProdutosEntradaMercadoriaManual"
                    isOpen={isOpenDrawer}
                    isLoading={isLoading}
                    isLoadingPesquisa={isLoadingPesquisa}
                    isLargerThan900={isLargerThan900}
                    isLargerThan1200={isLargerThan1200}
                    formMethods={drawerFormMethods}
                    onClose={onCloseDrawer}
                    handleLimparPesquisa={handleLimparPesquisa}
                    handleSubmit={handleSubmit}
                  >
                    <GridItem colSpan={12}>
                      <Input
                        id="nome"
                        name="nome"
                        placeholder="Digite o nome do produto"
                        maxLength={50}
                        label="Descrição do produto"
                        autoFocus
                      />
                    </GridItem>
                    <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                      <Input
                        id="sku"
                        placeholder="Digite um valor"
                        maxLength={50}
                        label="Código SKU"
                        name="sku"
                      />
                    </GridItem>
                    <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                      <Input
                        id="skuIdentificadorReferencia"
                        placeholder="Digite um valor"
                        maxLength={50}
                        label="Código/Referência"
                        name="skuIdentificadorReferencia"
                        helperText="Informe o código SKUIdentificador ou a referência do produto."
                      />
                    </GridItem>
                    <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                      <Input
                        id="codigoBarrasFornecedor"
                        placeholder="Digite um valor"
                        maxLength={50}
                        label="Código de barras"
                        name="codigoBarrasFornecedor"
                        helperText="Informe o GTIN/EAN ou código externo do produto."
                      />
                    </GridItem>
                    <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                      <Input
                        id="codigoBarrasEtiqueta"
                        placeholder="Digite um valor"
                        maxLength={50}
                        label="Código da etiqueta"
                        name="codigoBarrasEtiqueta"
                        helperText="Informe o código da etiqueta padrão ou reduzido."
                      />
                    </GridItem>
                    <GridItem colSpan={12}>
                      <SelectPadrao
                        label="Estoque"
                        required
                        id="tipoEstoque"
                        placeholder="Clique aqui para selecionar"
                        name="tipoEstoque"
                        options={TipoFiltroProdutoEstoqueEnum.produtos}
                      />
                    </GridItem>
                    <GridItem colSpan={12}>
                      <FormLabel
                        mb="1px"
                        fontSize="sm"
                        color="black"
                        lineHeight="1.2"
                      >
                        Categoria
                      </FormLabel>
                      <SelectCategoria
                        name="categorias"
                        placeholder="Digite o nome da categoria e clique para selecionar"
                      />
                    </GridItem>
                    <GridItem colSpan={12}>
                      <FormLabel
                        mb="1px"
                        fontSize="sm"
                        color="black"
                        lineHeight="1.2"
                      >
                        Marca
                      </FormLabel>
                      <SelectMulti
                        id="marcas"
                        name="marcas"
                        textLabelSelectAll="Todas as marcas"
                        placeholder="Digite o nome da marca e clique para selecionar"
                        variant="outline"
                        options={marcas}
                        isMulti
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleSubmit();
                          }
                        }}
                        isSearchable={false}
                        closeMenuOnSelect={false}
                      />
                    </GridItem>
                  </DrawerConsultaProdutos>
                </ModalContent>
              );
            }}
          </ConsultaProdutoPdvContext.Consumer>
        </ConsultaProdutosPdvProvider>
      </ModalPadraoChakra>
    );
  }
);
