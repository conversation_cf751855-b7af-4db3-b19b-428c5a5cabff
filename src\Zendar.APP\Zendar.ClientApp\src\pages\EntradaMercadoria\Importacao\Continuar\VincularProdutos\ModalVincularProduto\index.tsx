import {
  ModalContent,
  ModalBody,
  Icon,
  Flex,
  useDisclosure,
  ModalHeader,
  Text,
  ModalFooter,
  HStack,
  Button,
  VStack,
  GridItem,
} from '@chakra-ui/react';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create } from 'react-modal-promise';

import { ModalConsultaProdutosEntradaMercadoria } from 'pages/EntradaMercadoria/components/ConsultarProdutos';
import { MenuSelect } from 'pages/EntradaMercadoria/components/MenuSelect';
import {
  Tamanho,
  Quantidade,
} from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/components';
import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import CreatableSelect from 'components/PDV/Select/CreatableSelect';
import CreatableSelectVirtualized from 'components/PDV/Select/CreatableSelectVirtualized';
import { chakraComponents } from 'components/PDV/Select/ReactSelectIntegracao';
import { SimpleCard } from 'components/update/Form/SimpleCard';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { NumberInput } from 'components/update/Input/NumberInput';

import { EditarSituacaoIcon, LupaIcon } from 'icons';

import { ModalEditarCaracteristicas } from '../ModalEditarCaracteristicas';

import { InformacoesProduto } from './components/InformacoesProduto';
import { ListagemItemEdicao } from './components/ListagemItemEdicao';
import { ListagemVariacoesAdicionadas } from './components/ListagemVariacoesAdicionadas';
import { useVinculacao } from './hooks';
import { ModalVincularProdutoProps } from './types';
import { yupResolver, FormData } from './validationForm';

export const ModalVincularProduto = create<ModalVincularProdutoProps>(
  ({
    onResolve,
    onReject,
    casasDecimaisQuantidade,
    casasDecimaisValor,
    totalProdutos,
    produto,
    produtoJaVinculado,
    entradaMercadoriaId,
    isEdicao = false,
    numeroItem,
    proximoItem,
    produtoPendenteVariacoes,
    ...rest
  }) => {
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const formMethods = useForm<FormData>({
      resolver: yupResolver,
      defaultValues: {
        quantidade: produto.quantidade,
        valorUnitario: produto.valorUnitario,
        cfop: produto.cfopNota,
        codigoBarras: produto.codigoGTINEAN,
        codigoBarrasVariacao: null,
      },
    });

    const { watch } = formMethods;

    const {
      quantidade: quantidadeProduto,
      cfop: cfopProduto,
      codigoBarras: codigoBarrasProduto,
    } = watch();

    const temProximoProdutoParaVincular = isEdicao
      ? false
      : (proximoItem || 1) < totalProdutos;

    const {
      obterItemVinculado,
      onChangeSelectProduto,
      handleCadastrarProduto,
      validarPesquisa,
      handleSubmit,
      buscarProduto,
      handleCadastrarCor,
      quantidadeMaiorQueZero,
      adicionarVariacaoNaLista,
      handleCadastrarTamanho,
      abriModalVariacoes,
      handleAbrirModalEscolherGradeTamanhos,
      setListaVariacoes,
      limparValoresFiscaisVoltarValorPrecoCompra,
      setItemEdicao,
      alterarVinculacao,
      itemEdicao,
      listaVariacoes,
      isLoading,
      quantidadeTotal,
      quantidadeProdutoEscolhido,
      pesquisarPorLeitorWatch,
      isLoadingProduto,
      totalRegistros,
      produtoSelecionado,
      produtoDeVolumeUnitario,
      temGradeLancada,
      produtoTipoVariacao,
      produtoTemCores,
      coresDoProduto,
      corEscolhida,
      podeConfirmar,
      produtoTemTamanhos,
      tamanhoEscolhido,
      tamanhosDoProduto,
    } = useVinculacao({
      produto,
      formMethods,
      entradaMercadoriaId,
      casasDecimaisQuantidade,
      onResolve,
      onReject,
      temProximoProdutoParaVincular,
    });

    const dadosVinculacao = {
      codigoBarrasNota: produto.codigoBarrasNota,
      quantidadeNota: produto.quantidade,
      cfopNota: produto.cfopNota,
      descricaoProduto: produto.descricaoProduto,
      dadosAdicionais: produto?.dadosAdicionais,
      tipoProduto: produtoSelecionado?.tipoProduto || produto.tipoProduto,
      valorUnitarioNota: (
        produto.valorTotal / (produto.quantidade || 1)
      ).toFixed(6),
      quantidade: quantidadeProduto,
      valorUnitario: (produto.valorTotal / (quantidadeProduto || 1)).toFixed(6),
      cfop: cfopProduto,
      valorTotal: produto.valorTotal,
      codigoBarras: codigoBarrasProduto,
    };

    useEffect(() => {
      if (isEdicao) {
        obterItemVinculado();
      }
    }, [isEdicao]);

    const itemSendoEditado =
      isEdicao && Boolean((itemEdicao || [])?.length > 0);

    const desabilitarBotaoConfirmar =
      (produtoTipoVariacao &&
        !(quantidadeTotal === dadosVinculacao?.quantidade)) ||
      (!produtoTipoVariacao &&
        quantidadeProdutoEscolhido !== dadosVinculacao?.quantidade) ||
      (produtoTemCores &&
        !corEscolhida &&
        quantidadeTotal !== dadosVinculacao?.quantidade) ||
      (produtoTemTamanhos &&
        !tamanhoEscolhido &&
        quantidadeTotal !== dadosVinculacao?.quantidade);

    const exibirEditarCaracteristicas =
      !!produtoSelecionado || (itemEdicao?.length ?? 0) > 0;

    const abrirModalEditarCaracteristicas = async () => {
      const data = await ModalEditarCaracteristicas({
        casasDecimaisQuantidade,
        casasDecimaisValor,
        dadosVinculacao: dadosVinculacao,
        entradaMercadoriaId,
        estaEditando: Boolean(itemEdicao),
      });

      if (data.success) {
        formMethods.setValue('quantidade', data.quantidade);
        formMethods.setValue('cfop', data.cfop);
        formMethods.setValue('valorUnitario', data.valorUnitario);
        formMethods.setValue('codigoBarras', data.codigoBarras);
      }
    };

    useEffect(() => {
      if (produtoPendenteVariacoes) {
        onChangeSelectProduto({
          label: produtoPendenteVariacoes.nome,
          value: produtoPendenteVariacoes,
        });
      }
    }, [produtoPendenteVariacoes]);

    return (
      <ModalPadraoChakra
        isCentered
        size="full"
        appendToParentPortal={false}
        {...rest}
        isOpen={isOpen}
        onClose={onClose}
        scrollBehavior="inside"
      >
        <ModalContent h="unset" bg="gray.100" borderRadius="0px">
          {isLoading && <LoadingPadrao />}
          <ModalHeader
            px="40px"
            py="20px"
            pb="0px"
            color="violet.500"
            fontWeight="normal"
            fontSize="18px"
          >
            <Flex align="center" gap="4px" justify="flex-start">
              {itemSendoEditado
                ? 'Alterando a vinculação do produto'
                : `Vinculando`}
              {!itemSendoEditado && (
                <>
                  <Flex
                    width="fit-content"
                    height="32px"
                    justify="center"
                    align="center"
                    bg="secondary.300"
                    fontWeight="semibold"
                  >
                    {produtoJaVinculado ? (numeroItem ?? 0) - 1 : numeroItem}
                  </Flex>
                  <Flex
                    justify="center"
                    align="center"
                    height="32px"
                    w="fit-content"
                    fontWeight="semibold"
                  >
                    de {totalProdutos}
                    {' produtos'}
                  </Flex>
                </>
              )}
            </Flex>
          </ModalHeader>
          <Flex
            justify="flex-end"
            px="36px"
            pb="12px"
            gap="4px"
            onClick={abrirModalEditarCaracteristicas}
            cursor="pointer"
            opacity={exibirEditarCaracteristicas ? 1 : 0}
            pointerEvents={exibirEditarCaracteristicas ? 'all' : 'none'}
          >
            <Icon as={EditarSituacaoIcon} fontSize="16px" />
            <Text
              fontWeight="bold"
              fontSize="14px"
              color="black"
              w="fit-content"
            >
              Editar características
            </Text>
          </Flex>
          <ModalBody px="40px" pt="0px">
            <FormProvider {...formMethods}>
              <VStack spacing={{ base: 6, md: 8 }} alignItems="stretch">
                <InformacoesProduto
                  casasDecimaisQuantidade={casasDecimaisQuantidade}
                  casasDecimaisValor={casasDecimaisValor}
                  dadosVinculacao={dadosVinculacao}
                />

                <SimpleCard
                  bg="gray.50"
                  boxShadow="none"
                  p="24px 24px 32px 24px"
                >
                  <SimpleGridForm gap="24px">
                    <GridItem
                      colSpan={[12, 12, 12, 9]}
                      bg="gray.50"
                      as={Flex}
                      alignItems="flex-end"
                      flexDir={{ base: 'column', md: 'row' }}
                      gap={{ base: 4, md: 8, lg: '12px' }}
                    >
                      <CreatableSelectVirtualized
                        id="produto"
                        name="produto"
                        placeholder={
                          pesquisarPorLeitorWatch
                            ? 'Utilize um leitor de código de barras'
                            : 'Digite o nome do produto existente no sistema ou cadastre um novo'
                        }
                        label="Para vincular, encontre o cadastro do produto:"
                        handleGetOptions={buscarProduto}
                        isLoading={isLoadingProduto}
                        creatableButtonShow={!pesquisarPorLeitorWatch}
                        creatableInputTextPreffix="Cadastrar o produto"
                        handleCreateOption={(inputValue) => {
                          if (!validarPesquisa(inputValue)) {
                            return handleCadastrarProduto(inputValue);
                          }
                        }}
                        onChangeSelect={onChangeSelectProduto}
                        required
                        asControlledByObject
                        disabled={itemSendoEditado}
                        isClearable
                        autoFocus
                        closeMenuOnSelect
                        totalRegistros={totalRegistros}
                      />

                      {!itemEdicao && (
                        <Flex
                          height="36px"
                          width="max"
                          gap="6px"
                          px={2}
                          cursor="pointer"
                          alignItems="center"
                          justify="flex-end"
                        >
                          <Icon
                            as={LupaIcon}
                            fontSize="20px"
                            color="purple.500"
                          />
                          <Text
                            color="purple.500"
                            textDecor="underline"
                            fontSize="16px"
                            width="max"
                            role="button"
                            onClick={async () => {
                              if (isLoadingProduto) return;
                              const response =
                                await ModalConsultaProdutosEntradaMercadoria({
                                  casasDecimais: {
                                    casasDecimaisQuantidade,
                                  },
                                });

                              const produtoOpcao = {
                                label: response.nome,
                                value: {
                                  ...response,
                                  coresOptions: [],
                                  tamanhosOptions: [],
                                } as ProdutoOptionProps,
                              };

                              onChangeSelectProduto(produtoOpcao);
                            }}
                          >
                            Consultar produtos
                          </Text>
                        </Flex>
                      )}
                    </GridItem>
                    {itemSendoEditado && (
                      <>
                        <GridItem
                          colSpan={[12, 12, 12, 3]}
                          bg="gray.50"
                          as={Flex}
                          alignItems="flex-end"
                          flexDir={{ base: 'column', md: 'row' }}
                          gap={{ base: 4, md: 8, lg: '12px' }}
                        >
                          <NumberInput
                            id="custoAdicional"
                            name="custoAdicional"
                            bgLeftElement="gray.50"
                            editarFundoLeftElemento
                            leftElementFontSize="xs"
                            label="Custo adicional"
                            leftElement="R$"
                            placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
                            scale={casasDecimaisValor}
                            colSpan={4}
                          />
                        </GridItem>
                        <ListagemItemEdicao
                          casasDecimaisQuantidade={casasDecimaisQuantidade}
                          itens={Array.isArray(itemEdicao) ? itemEdicao : []}
                          removerItemEditado={setItemEdicao}
                        />
                      </>
                    )}
                    {!!produtoSelecionado && !isLoadingProduto && (
                      <>
                        {produtoTipoVariacao && (
                          <GridItem
                            colSpan={12}
                            mb="-18px"
                            fontSize="14px"
                            fontWeight="bold"
                          >
                            Selecione uma variação
                          </GridItem>
                        )}
                        <GridItem
                          colSpan={produtoTipoVariacao ? 12 : 6}
                          bg={produtoTipoVariacao ? 'gray.100' : 'gray.50'}
                          padding={produtoTipoVariacao ? '24px' : '0px'}
                          borderRadius="5px"
                        >
                          <SimpleGridForm gap="24px" columns={24}>
                            {produtoTemCores && (
                              <GridItem
                                colSpan={
                                  produtoTipoVariacao
                                    ? { base: 12, md: 10, lg: 6 }
                                    : 6
                                }
                              >
                                <CreatableSelect
                                  id="cor"
                                  name="cor"
                                  label="Cor"
                                  getOptionLabel={(option) => option.label}
                                  placeholder="Selecione uma cor cadastrada no produto"
                                  creatableInputTextPreffix="Cadastrar a cor"
                                  colSpan={[12, 4, 4, 4]}
                                  handleCreateOption={handleCadastrarCor}
                                  options={coresDoProduto}
                                  disabled={isLoadingProduto}
                                  asControlledByObject
                                  actionLinkText="Adicionar nova"
                                  actionLinkOnClick={abriModalVariacoes}
                                  onChangeSelect={(corSelecionada: any) => {
                                    formMethods.setValue(
                                      'codigoBarrasVariacao',
                                      corSelecionada.codigoBarras ?? null
                                    );
                                    formMethods.setValue(
                                      'listaTamanhoIdQuantidade.0.tamanho',
                                      null
                                    );
                                  }}
                                  required
                                  components={{
                                    ...chakraComponents,
                                    MenuList: (props: any) => (
                                      <MenuSelect
                                        texto={
                                          coresDoProduto?.length > 0
                                            ? '**Exibindo apenas cores ativas do produto**'
                                            : ''
                                        }
                                        {...props}
                                      />
                                    ),
                                  }}
                                />
                              </GridItem>
                            )}
                            {produtoTemTamanhos && (
                              <GridItem
                                colSpan={
                                  produtoTipoVariacao
                                    ? { base: 12, md: 10, lg: 6 }
                                    : 6
                                }
                              >
                                <Tamanho
                                  temGradeLancada={temGradeLancada}
                                  tamanhosDoProduto={tamanhosDoProduto}
                                  handleCadastrarTamanho={
                                    handleCadastrarTamanho
                                  }
                                  isLoadingProduto={isLoadingProduto}
                                  produtoTemCores={produtoTemCores}
                                  corEscolhida={corEscolhida}
                                  handleAbrirModalEscolherGradeTamanhos={
                                    handleAbrirModalEscolherGradeTamanhos
                                  }
                                  produtoTemTamanhos={produtoTemTamanhos}
                                  abrirModalVariacoes={abriModalVariacoes}
                                  placeholder="Selecione"
                                  onChangeSelect={(tamanho: any) => {
                                    formMethods.setValue(
                                      'codigoBarrasVariacao',
                                      tamanho?.codigoBarras ?? null
                                    );
                                  }}
                                />
                              </GridItem>
                            )}
                            <GridItem
                              colSpan={
                                produtoTipoVariacao
                                  ? { base: 12, md: 6, lg: 3 }
                                  : 8
                              }
                            >
                              <Quantidade
                                produtoTemGradeLancada={temGradeLancada}
                                casasDecimaisQuantidade={
                                  casasDecimaisQuantidade
                                }
                                produtoDeVolumeUnitario={
                                  !!produtoDeVolumeUnitario
                                }
                                produtoPossuiVariacoes={produtoTipoVariacao}
                                quantidade={formMethods
                                  ?.watch('listaTamanhoIdQuantidade')
                                  ?.reduce(
                                    (
                                      acc: number,
                                      curr: {
                                        quantidade: number;
                                      }
                                    ) => acc + curr.quantidade,
                                    0
                                  )}
                                manterTamanho
                                quantidadeProdutoParaVincular={
                                  produtoTipoVariacao
                                    ? quantidadeProduto - quantidadeTotal
                                    : quantidadeProduto
                                }
                              />
                            </GridItem>
                            <GridItem
                              colSpan={
                                produtoTipoVariacao
                                  ? { base: 12, md: 6, lg: 3 }
                                  : 8
                              }
                              w="full"
                            >
                              <NumberInput
                                id="custoAdicional"
                                name="custoAdicional"
                                bgLeftElement="gray.50"
                                editarFundoLeftElemento
                                leftElementFontSize="xs"
                                label="Custo adicional"
                                w="full"
                                leftElement="R$"
                                placeholder={`0,${'0'.repeat(
                                  casasDecimaisValor
                                )}`}
                                scale={casasDecimaisValor}
                                isDisabled={
                                  !produtoSelecionado ||
                                  !!listaVariacoes?.length
                                }
                                colSpan={12}
                              />
                            </GridItem>
                            {produtoTipoVariacao && (
                              <>
                                <GridItem colSpan={{ base: 12, md: 6, lg: 3 }}>
                                  <NumberInput
                                    id="codigoBarrasVariacao"
                                    name="codigoBarrasVariacao"
                                    label="Código de Barras"
                                    w="full"
                                    pl="12px"
                                    bg="white"
                                    bgLeftElement="gray.50"
                                    editarFundoLeftElemento
                                    leftElementFontSize="xs"
                                    placeholder="Não informado"
                                    textAlign="left"
                                    precision={14}
                                    scale={0}
                                    canBeUndefined
                                  />
                                </GridItem>
                                <GridItem colSpan={[24, 3]} mt="18px">
                                  <Button
                                    colorScheme="teal"
                                    borderRadius="full"
                                    w={['full', 'full', 'full']}
                                    height="36px"
                                    fontSize="14px"
                                    minW="176px"
                                    onClick={adicionarVariacaoNaLista}
                                    isDisabled={
                                      !podeConfirmar ||
                                      !quantidadeMaiorQueZero() ||
                                      quantidadeTotal ===
                                        dadosVinculacao.quantidade
                                    }
                                    fontWeight="600"
                                  >
                                    Adicionar variação
                                  </Button>
                                </GridItem>
                              </>
                            )}
                          </SimpleGridForm>
                          <ListagemVariacoesAdicionadas
                            listaVariacoes={listaVariacoes}
                            setListaVariacoes={setListaVariacoes}
                            casasDecimaisQuantidade={casasDecimaisQuantidade}
                            produtoTemCores={produtoTemCores}
                            produtoTemTamanhos={produtoTemTamanhos}
                            limparValoresFiscaisVoltarValorPrecoCompra={
                              limparValoresFiscaisVoltarValorPrecoCompra
                            }
                            produtoParaVincular={dadosVinculacao}
                          />
                        </GridItem>
                      </>
                    )}
                  </SimpleGridForm>
                </SimpleCard>
              </VStack>
            </FormProvider>
          </ModalBody>
          <ModalFooter
            p="16px 32px"
            justifyContent={['center', 'center', 'flex-end']}
            borderTop="1px"
            position="sticky"
            borderColor="purple.500"
            mx={{ base: 0, md: 8 }}
          >
            <HStack justify="space-between" w="full">
              <Button
                colorScheme="gray"
                variant="outlineDefault"
                onClick={() => {
                  onReject();
                  onClose();
                }}
                borderRadius="full"
                minW="160px"
                height="32px"
                fontSize="14px"
                fontWeight="600"
                w={['full', isEdicao ? '160px' : 'full', '160px']}
              >
                Voltar para lista
              </Button>
              <Flex
                justifyContent={['center', 'center', 'flex-end']}
                direction={['column-reverse', 'column-reverse', 'row']}
                gap={['12px', '12px', '24px']}
              >
                {!itemSendoEditado && temProximoProdutoParaVincular && (
                  <Button
                    variant={
                      desabilitarBotaoConfirmar ? 'solid' : 'outlineDefault'
                    }
                    color={desabilitarBotaoConfirmar ? 'white' : 'inherit'}
                    colorScheme="gray"
                    borderRadius="full"
                    height="32px"
                    fontSize="14px"
                    isDisabled={desabilitarBotaoConfirmar}
                    fontWeight="600"
                    w={['full', 'full', '160px']}
                    onClick={() =>
                      formMethods.handleSubmit((data) =>
                        handleSubmit({ data, vincularProximoItem: false })
                      )()
                    }
                  >
                    Confirmar e sair
                  </Button>
                )}
                {itemSendoEditado ? (
                  <Button
                    w={['full', 'full', '320px']}
                    height="32px"
                    fontSize="14px"
                    fontWeight="600"
                    colorScheme="violet"
                    borderRadius="full"
                    onClick={alterarVinculacao}
                    isDisabled={!((itemEdicao || [])?.length > 0)}
                  >
                    Confirmar e salvar alterações
                  </Button>
                ) : (
                  <Button
                    w={['full', 'full', '320px']}
                    height="32px"
                    fontSize="14px"
                    fontWeight="600"
                    colorScheme="violet"
                    borderRadius="full"
                    onClick={() =>
                      formMethods.handleSubmit((data) =>
                        handleSubmit({ data })
                      )()
                    }
                    isDisabled={desabilitarBotaoConfirmar}
                  >
                    {temProximoProdutoParaVincular && !isEdicao
                      ? 'Confirmar e ir para o próximo produto'
                      : 'Confirmar e vincular produto'}
                  </Button>
                )}
              </Flex>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
