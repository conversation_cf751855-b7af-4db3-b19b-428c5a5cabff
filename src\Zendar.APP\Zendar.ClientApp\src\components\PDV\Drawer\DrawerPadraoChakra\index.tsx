import { Drawer, DrawerProps, DrawerOverlay } from '@chakra-ui/react';
import React from 'react';

import PortalFullscreen from 'components/PDV/Geral/PortalFullscreen';

interface PropsDrawerPadraoChakra extends DrawerProps {
  children: any;
  isOverlay?: boolean;
  appendToParentPortal?: boolean;
}

const DrawerPadraoChakra = ({
  children,
  isOverlay = true,
  appendToParentPortal = true,
  ...props
}: PropsDrawerPadraoChakra) => {
  return (
    <PortalFullscreen appendToParentPortal={appendToParentPortal}>
      <Drawer autoFocus {...props}>
        {isOverlay ? (
          <DrawerOverlay zIndex="modal">{children}</DrawerOverlay>
        ) : (
          children
        )}
      </Drawer>
    </PortalFullscreen>
  );
};

export default DrawerPadraoChakra;
