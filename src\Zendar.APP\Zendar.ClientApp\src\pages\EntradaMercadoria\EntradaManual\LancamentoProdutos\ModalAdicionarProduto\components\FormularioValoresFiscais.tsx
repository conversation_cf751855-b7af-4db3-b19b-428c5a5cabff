import React from 'react';

import { NumberInput } from 'components/update/Input/NumberInput';

export const FormularioValoresFiscais = ({
  entradaRateiaIcmsSt,
  casasDecimaisValor,
  listaJaPossuiProdutoAdicionado,
  estaEditando,
}: {
  entradaRateiaIcmsSt: boolean;
  casasDecimaisValor: number;
  listaJaPossuiProdutoAdicionado: boolean;
  estaEditando?: boolean;
}) => {
  return (
    <>
      <NumberInput
        id="valorUnitario"
        name="valorUnitario"
        label="Valor unitário"
        bgLeftElement="gray.50"
        editarFundoLeftElemento
        leftElement="R$"
        leftElementFontSize="xs"
        placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
        scale={casasDecimaisValor}
        colSpan={[12, 6, 4, 2]}
        isDisabled={!estaEditando ? listaJaPossuiProdutoAdicionado : false}
      />

      <NumberInput
        id="ipi"
        name="ipi"
        bgLeftElement="gray.50"
        editarFundoLeftElemento
        label="IPI"
        leftElement="R$"
        leftElementFontSize="xs"
        placeholder={`0,${'0'.repeat(2)}`}
        scale={2}
        colSpan={[12, 6, 4, 2]}
        isDisabled={!estaEditando ? listaJaPossuiProdutoAdicionado : false}
      />
      <NumberInput
        id="icmsSt"
        name="icmsSt"
        bgLeftElement="gray.50"
        editarFundoLeftElemento
        label="ICMS ST"
        leftElement="R$"
        leftElementFontSize="xs"
        helperText="Informe o valor do ICMS ST somente para este produto. Se preferir, é possível informar o valor total contido na nota fiscal de uma só vez. Para isso ignore este campo e preencha o valor completo na próxima etapa: “valores”"
        placeholder={`0,${'0'.repeat(2)}`}
        scale={2}
        isDisabled={
          entradaRateiaIcmsSt || !estaEditando
            ? listaJaPossuiProdutoAdicionado
            : false
        }
        colSpan={[12, 6, 4, 2]}
      />
      <NumberInput
        id="fcpSt"
        name="fcpSt"
        bgLeftElement="gray.50"
        editarFundoLeftElemento
        label="FCP ST"
        leftElement="R$"
        leftElementFontSize="xs"
        placeholder={`0,${'0'.repeat(2)}`}
        scale={2}
        colSpan={[12, 6, 4, 2]}
        isDisabled={!estaEditando ? listaJaPossuiProdutoAdicionado : false}
      />

      <NumberInput
        id="custoAdicional"
        name="custoAdicional"
        bgLeftElement="gray.50"
        editarFundoLeftElemento
        leftElementFontSize="xs"
        label="Custo adicional"
        leftElement="R$"
        helperText="Os valores deste campo não serão somados ao valor total da entrada, servindo apenas para compor o custo do produto."
        placeholder={`0,${'0'.repeat(2)}`}
        scale={2}
        colSpan={[12, 12, 4, 2]}
        isDisabled={!estaEditando ? listaJaPossuiProdutoAdicionado : false}
      />
    </>
  );
};
