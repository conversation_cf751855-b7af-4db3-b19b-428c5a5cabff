import {
  ModalContent,
  ModalBody,
  Flex,
  useMediaQuery,
  useDisclosure,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  <PERSON><PERSON>,
  <PERSON>,
} from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { create } from 'react-modal-promise';

import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';

import TipoProdutoEnum from 'constants/enum/tipoProduto';

import { BoxCFOP } from './components/BoxCFOP';
import { BoxCodigoBarras } from './components/BoxCodigoBarras';
import { BoxQuantidade } from './components/BoxQuantidade';
import { BoxValorUnitario } from './components/BoxValorUnitario';
import { ModalEditarCaracteristicasProps, FormData } from './types';
import { yupResolver } from './validationForm';

export const ModalEditarCaracteristicas =
  create<ModalEditarCaracteristicasProps>(
    ({
      onResolve,
      onReject,
      casasDecimaisQuantidade,
      casasDecimaisValor,
      dadosVinculacao,
      entradaMercadoriaId,
      estaEditando = false,
      ...rest
    }) => {
      const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
      const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

      const formMethods = useForm<FormData>({
        resolver: yupResolver,
        defaultValues: {
          quantidade: dadosVinculacao.quantidade,
          cfop: dadosVinculacao.cfop,
          valorUnitario: dadosVinculacao.valorUnitario,
          codigoBarras: dadosVinculacao.codigoBarras || null,
        },
      });

      const handleSubmit = formMethods.handleSubmit(async (data) => {
        onResolve({
          success: true,
          quantidade: data.quantidade,
          cfop: data.cfop,
          valorUnitario: data.valorUnitario,
          codigoBarras: data.codigoBarras ? String(data.codigoBarras) : null,
        });
      });

      return (
        <ModalPadraoChakra
          isCentered
          size={!isSmallerThan900 ? '5xl' : 'full'}
          appendToParentPortal={false}
          {...rest}
          isOpen={isOpen}
          onClose={onClose}
          scrollBehavior="inside"
        >
          <ModalContent bg="gray.50" maxWidth="920px">
            <ModalHeader pt="30px" pb="28px" px="36px">
              <Text fontSize="16px" textColor="primary.50">
                Editar características
              </Text>
            </ModalHeader>
            <ModalBody
              pt="44px"
              bg="gray.100"
              borderTopWidth="1px"
              borderTopColor="#BBBBBB"
              borderBottomWidth="1px"
              borderBottomColor="#BBBBBB"
              minH="320px"
              px={{ base: '16px', md: '36px' }}
            >
              <FormProvider {...formMethods}>
                <Flex gap="16px" justify="center" flexWrap="wrap">
                  <BoxQuantidade
                    dadosVinculacao={dadosVinculacao}
                    formMethods={formMethods}
                    estaEditando={estaEditando}
                    casasDecimaisQuantidade={casasDecimaisQuantidade}
                  />
                  <BoxValorUnitario
                    dadosVinculacao={dadosVinculacao}
                    formMethods={formMethods}
                    estaEditando={estaEditando}
                    casasDecimaisValor={casasDecimaisValor}
                  />
                  <BoxCFOP dadosVinculacao={dadosVinculacao} />
                  {dadosVinculacao.tipoProduto ===
                    TipoProdutoEnum.PRODUTO_SIMPLES && (
                    <BoxCodigoBarras dadosVinculacao={dadosVinculacao} />
                  )}
                </Flex>
              </FormProvider>
            </ModalBody>
            <ModalFooter
              justifyContent="center"
              px={{ base: 0, sm: 2, md: 8 }}
              py="24px"
              gap={6}
              flexWrap="wrap-reverse"
            >
              <Button
                maxW="96px"
                height="32px"
                borderRadius="full"
                color="gray.5 00"
                variant="outlineDefault"
                fontSize="14px"
                onClick={onClose}
              >
                Cancelar
              </Button>
              <Button
                height="32px"
                minWidth={{ base: '200px', md: '200px' }}
                colorScheme="aquamarine"
                borderRadius="full"
                fontSize="14px"
                onClick={handleSubmit}
              >
                Salvar alterações
              </Button>
            </ModalFooter>
          </ModalContent>
        </ModalPadraoChakra>
      );
    }
  );
