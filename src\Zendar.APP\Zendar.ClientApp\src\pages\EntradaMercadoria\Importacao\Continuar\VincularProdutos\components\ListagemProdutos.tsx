import { Box, Skeleton, Td, Tr, useMediaQuery } from '@chakra-ui/react';
import { useMemo, useRef } from 'react';
import { CellMeasurerCache, Index } from 'react-virtualized';

import useWindowSize from 'helpers/layout/useWindowSize';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import {
  VirtualizedInfiniteTable,
  TableHeader,
  LoadMoreRowsParams,
} from 'components/update/Table/VirtualizedInfiniteTable';

import { Produto, InformacoesRodape } from '../hooks/useProdutosVinculacao';

import { ItemProduto } from './ItemProduto';

interface ListagemProdutosProps {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  isLoading: boolean;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditar: (index: number) => Promise<void>;
  handleVincularProduto: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => Promise<void>;
  loadMoreRows: (params: LoadMoreRowsParams) => Promise<void>;
  modoTelaCheia?: boolean;
}

const cache = new CellMeasurerCache({
  defaultHeight: 65,
  minHeight: 52,
  fixedWidth: true,
});

export function ListagemProdutos({
  produtos,
  informacoesRodape,
  isLoading,
  handleToggleLinhaProduto,
  handleEditar,
  handleVincularProduto,
  loadMoreRows,
  modoTelaCheia = false,
}: ListagemProdutosProps) {
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const { height: windowHeight } = useWindowSize();

  const produtosTableHeaders: TableHeader[] = useMemo(
    () => [
      {
        key: 'descricaoProduto',
        content: 'Produto',
        width: '70%',
        minWidth: '70%',
        paddingLeft: '48px !important',
      },
      {
        key: 'quantidade',
        content: 'Quantidade',
        isNumeric: false,
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
      {
        key: 'valorUnitario',
        content: 'Valor unitário',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'valorTotal',
        content: 'Valor total',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'acoes',
        width: '180px',
        minWidth: '180px',
        content: 'Ações',
        textAlign: 'end',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
    ],
    []
  );

  const getFixedTableHeight = (): number => {
    const maxVisibleItems = 8;
    const averageItemHeight = 64;
    return maxVisibleItems * averageItemHeight;
  };

  const maxContainerHeight = useMemo((): string => {
    const stepDescriptionHeight = 80;
    const containerPadding = 48;
    const totalizadoresHeight = informacoesRodape.totalProdutos > 0 ? 145 : 0;
    const footerHeight = isLargerThan900 ? 70 : 0;
    const marginBottom = 24;

    const availableHeight =
      windowHeight -
      stepDescriptionHeight -
      containerPadding -
      totalizadoresHeight -
      footerHeight -
      marginBottom;

    const dynamicHeight = Math.max(availableHeight, 300);

    return `${dynamicHeight}px`;
  }, [windowHeight, isLargerThan900, informacoesRodape.totalProdutos]);

  const alturaDisponivelModal = useMemo(() => {
    if (!modoTelaCheia) return 0;
    const offsetHeaderModal = 120;
    const altura = Math.max(windowHeight - offsetHeaderModal, 300);
    return altura;
  }, [modoTelaCheia, windowHeight]);

  const alturaTabelaVirtualizada = useMemo(() => {
    if (!modoTelaCheia) return 0;
    const offsetThead = 64;
    const altura = Math.max(alturaDisponivelModal - offsetThead, 200);
    return altura;
  }, [modoTelaCheia, alturaDisponivelModal]);

  const itemAlturaBase = 60;
  const pageSizeTelaCheia = useMemo(() => {
    if (!modoTelaCheia) return 25;
    const estimativaVisiveis = Math.ceil(
      alturaTabelaVirtualizada / itemAlturaBase
    );
    return Math.max(25, estimativaVisiveis + 8);
  }, [modoTelaCheia, alturaTabelaVirtualizada]);

  const getDynamicHeight = (index: number, marginSize = 10): number => {
    const produto = produtos[index];
    if (!produto) return 56 + marginSize;

    const isLastItem = index === produtos.length - 1;
    const closedProdutoHeight = 56 + (isLastItem ? 0 : marginSize);
    const openedProdutoHeight = 124 + (isLastItem ? 0 : marginSize);
    const produtoHeight = produto.isOpen
      ? openedProdutoHeight
      : closedProdutoHeight;
    return produtoHeight;
  };

  const isLoadingMoreRows = useRef(false);

  const handleLoadMoreRows = async (
    params: LoadMoreRowsParams
  ): Promise<void> => {
    if (isLoading || isLoadingMoreRows.current) {
      return;
    }

    isLoadingMoreRows.current = true;
    await loadMoreRows(params);
    isLoadingMoreRows.current = false;
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      borderRadius="md"
      border="1px"
      bg="gray.50"
      borderColor="gray.200"
      maxH={modoTelaCheia ? `${alturaDisponivelModal}px` : maxContainerHeight}
      py={{ base: 4, sm: 6, md: 6 }}
      pl={{ base: 4, sm: 6, md: 6 }}
      pb={modoTelaCheia ? '60px' : 0}
      pr={{ base: '6px', sm: '14px', md: '24px' }}
      sx={{
        '& table': { bg: 'gray.50' },
        '& thead > tr > th': {
          bg: 'gray.50',
          border: 'none',
        },
        '& td:first-of-type': {
          paddingLeft: '16px !important',
        },
        '& tbody > tr': {
          borderRadius: 'md',
          boxShadow: '0px 0px 2px #00000029',
          ...(informacoesRodape.totalProdutos > 0
            ? { border: '1px', borderColor: 'gray.100' }
            : {
                '& > td': {
                  position: 'relative',
                  _before: {
                    content: '""',
                    position: 'absolute',
                    h: 'full',
                    w: 'full',
                    top: 0,
                    left: 0,
                    borderLeft: 'none',
                    borderRight: 'none',
                    borderRadius: 'md',
                  },
                },
              }),
        },
        '& tbody > tr > td': {
          bg: 'white',
          lineHeight: 'none',
          _before: {
            border:
              informacoesRodape.totalProdutos > 0 ? 'none !important' : '1px',
            borderColor: 'gray.100',
          },
        },
      }}
    >
      <VirtualizedInfiniteTable
        variant="simple-card"
        size="sm"
        bg="gray.50"
        boxShadow="none"
        withoutRowsMessage="Nenhum produto adicionado."
        orderColumn="descricaoProduto"
        tableHeaders={produtosTableHeaders}
        itemHeight={60}
        degradeSuperior="linear-gradient(to bottom, #f5f5f5 0%, rgb(255 255 255 / 30%) 100%)"
        degradeInferior="linear-gradient(to top, #f5f5f5 0%, rgb(245 245 245 / 30%) 100%)"
        visibleItemsCount={8}
        dynamicHeight={({ index }: Index) => getDynamicHeight(index, 10)}
        rowCount={informacoesRodape.totalProdutos}
        isRowLoaded={({ index }: Index) => !!produtos[index]}
        loadMoreRows={handleLoadMoreRows}
        heightTable={
          modoTelaCheia ? alturaTabelaVirtualizada : getFixedTableHeight()
        }
        isLoading={isLoading}
        loadedCount={produtos?.length}
        pageSize={modoTelaCheia ? pageSizeTelaCheia : 25}
        rowRenderer={({ index, style, key, parent }) => {
          const produto = produtos[index];

          if (!produto) {
            return (
              <>
                <Tr key={key} style={style}>
                  <Td colSpan={produtosTableHeaders.length}>
                    <Skeleton h="56px" />
                  </Td>
                </Tr>
              </>
            );
          }

          return (
            <ItemProduto
              key={key}
              produto={produto}
              index={index}
              cache={cache}
              style={style}
              parent={parent}
              produtosTableHeaders={produtosTableHeaders}
              onToggle={handleToggleLinhaProduto}
              onEditar={handleEditar}
              onVincular={handleVincularProduto}
              getDynamicHeight={getDynamicHeight}
            />
          );
        }}
      />
    </Box>
  );
}
