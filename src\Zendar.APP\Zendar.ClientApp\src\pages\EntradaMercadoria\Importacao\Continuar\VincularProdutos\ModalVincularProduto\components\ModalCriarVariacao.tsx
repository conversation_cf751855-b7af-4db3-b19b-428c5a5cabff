import {
  ModalContent,
  ModalBody,
  useMediaQuery,
  useDisclosure,
  ModalHeader,
  ModalFooter,
  Text,
  Button,
  Flex,
  ModalProps,
  IconButton,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FaPlus } from 'react-icons/fa';
import { IoClose } from 'react-icons/io5';
import { create, InstanceProps } from 'react-modal-promise';
import { OptionTypeBase } from 'react-select';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import api, { ResponseApi } from 'services/api';

import { ModalAtivarVariacao } from 'pages/Produtos/Formulario/TabsContent/Variacoes/components/ModalAtivarVariacao';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import CreatableSelect from 'components/PDV/Select/CreatableSelect';
import { StatusCircle } from 'components/update/Table/StatusCircle';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import ConstanteFuncionalidades from 'constants/permissoes';

interface ModalCriarVariacaoProps
  extends Omit<ModalProps, 'children' | 'isOpen' | 'onClose'>,
    InstanceProps<any> {
  produtoId: string;
}

type OptionProps = {
  value: string;
  label: string;
  ativo: boolean;
  podeExcluir?: boolean;
};

export const ModalCriarVariacao = create<ModalCriarVariacaoProps>(
  ({ onResolve, onReject, produtoId, ...rest }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [listaCores, setListaCores] = useState<OptionProps[]>([]);
    const [listaTamanhos, setListaTamanhos] = useState<OptionProps[]>([]);
    const [coresDoSistema, setCoresDoSistema] = useState<OptionProps[]>([]);
    const [tamanhosDoSistema, setTamanhosDoSistema] = useState<OptionProps[]>(
      []
    );

    const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const { permitido: temPermissaoCadastrarCor } = auth.possuiPermissao(
      ConstanteFuncionalidades.COR_CADASTRAR
    );
    const { permitido: temPermissaoCadastrarTamanho } = auth.possuiPermissao(
      ConstanteFuncionalidades.TAMANHO_CADASTRAR
    );

    const formMethods = useForm<{
      cor: null | {
        value: string;
        label: string;
        ativo: boolean;
      };
      tamanho: null | {
        value: string;
        label: string;
        ativo: boolean;
      };
    }>({
      defaultValues: {
        cor: null,
        tamanho: null,
      },
    });

    const corEscolhida = Boolean(formMethods.watch('cor'));
    const tamanhoEscolhido = Boolean(formMethods.watch('tamanho'));

    const handleRemoverVariacao = useCallback(
      async (
        data: {
          value: string;
          label: string;
          ativo: boolean;
          podeExcluir?: boolean;
        },
        tipo: 'cores' | 'tamanhos'
      ) => {
        setIsLoading(true);
        const response = await api.delete<void, ResponseApi>(
          `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoId}/${tipo}/${data.value}`
        );
        if (response) {
          if (response.avisos) {
            response.avisos.forEach((aviso) => toast.warning(aviso));
          }
          if (response.sucesso) {
            if (tipo === 'cores') {
              setListaCores((prevState) =>
                prevState.filter((cor) => cor.value !== data.value)
              );
              setCoresDoSistema((prevState) => [
                ...prevState,
                {
                  ...data,
                  ativo: true,
                  podeExcluir: false,
                },
              ]);
            } else {
              setListaTamanhos((prevState) =>
                prevState.filter((tamanho) => tamanho.value !== data.value)
              );
              setTamanhosDoSistema((prevState) => [
                ...prevState,
                {
                  ...data,
                  ativo: true,
                  podeExcluir: false,
                },
              ]);
            }
          }
        }
        setIsLoading(false);
      },
      [produtoId]
    );

    const handleAdicionarVariacaoCor = useCallback(
      async (data: { value: string; label: string; ativo: boolean } | null) => {
        if (!data) return;
        setIsLoading(true);
        const response = await api.post<void, ResponseApi>(
          `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoId}/cores`,
          {
            id: data.value,
            nome: data.label,
          }
        );

        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
            setIsLoading(false);

            return false;
          }

          if (response.sucesso) {
            setCoresDoSistema((prevState) => {
              const corExistente = prevState.find(
                (cor) => cor.value === data.value
              );
              if (corExistente) {
                return prevState.filter((item) => item.value !== data.value);
              }
              return prevState;
            });

            setListaCores((prevState) => [
              ...prevState,
              {
                ...data,
                podeExcluir: true,
              },
            ]);

            formMethods.setValue('cor', null);
            setIsLoading(false);

            return true;
          }
          setIsLoading(false);

          return false;
        }
        setIsLoading(false);

        return false;
      },
      [formMethods, produtoId]
    );

    const handleAdicionarVariacaoTamanho = useCallback(
      async (data: { value: string; label: string; ativo: boolean } | null) => {
        if (!data) return;
        setIsLoading(true);

        const response = await api.post<void, ResponseApi>(
          `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoId}/tamanhos`,
          {
            id: data.value,
            nome: data.label,
          }
        );

        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
            setIsLoading(false);

            return false;
          }

          if (response.sucesso) {
            setTamanhosDoSistema((prevState) => {
              const tamanhoExistente = prevState.find(
                (tamanho) => tamanho.value === data.value
              );
              if (tamanhoExistente) {
                return prevState.filter((item) => item.value !== data.value);
              }
              return prevState;
            });

            setListaTamanhos((prevState) => [
              ...prevState,
              {
                ...data,
                podeExcluir: true,
              },
            ]);

            formMethods.setValue('tamanho', null);
            setIsLoading(false);

            return true;
          }
          setIsLoading(false);

          return false;
        }
        setIsLoading(false);

        return false;
      },
      [formMethods, produtoId]
    );

    const handleCadastrarTamanho = useCallback(
      async (tamanhoDescricao: string) => {
        if (!temPermissaoCadastrarTamanho) {
          toast.warning(
            'Você não tem permissão para acessar essa função. Consulte o administrador da conta.'
          );
          return undefined;
        }
        setIsLoading(true);
        const response = await api.post<void, ResponseApi>(
          ConstanteEnderecoWebservice.TAMANHO_CADASTRAR,
          { descricao: tamanhoDescricao, ativo: true }
        );

        if (response?.sucesso) {
          const tamanhoId = response.dados;
          const novoTamanho = {
            label: tamanhoDescricao,
            value: tamanhoId as string,
            ativo: true,
            podeExcluir: true,
          };

          await handleAdicionarVariacaoTamanho(novoTamanho);
          setIsLoading(false);
          return undefined;
        }

        if (response?.avisos) {
          response.avisos.map((item: string) => toast.warning(item));
        }
        setIsLoading(false);
        return undefined;
      },
      [handleAdicionarVariacaoTamanho, temPermissaoCadastrarTamanho]
    );

    const handleCadastrarCor = useCallback(
      async (corDescricao: string) => {
        if (!temPermissaoCadastrarCor) {
          toast.warning(
            'Você não tem permissão para acessar essa função. Consulte o administrador da conta.'
          );
          return undefined;
        }
        setIsLoading(true);
        const response = await api.post<void, ResponseApi>(
          ConstanteEnderecoWebservice.COR_CADASTRAR,
          { descricao: corDescricao, ativo: true }
        );

        if (response?.sucesso) {
          const corId = response.dados;
          const novaCor = {
            label: corDescricao,
            value: corId as string,
            ativo: true,
            podeExcluir: true,
          };
          await handleAdicionarVariacaoCor(novaCor);
          setIsLoading(false);
          return undefined;
        }

        if (response?.avisos) {
          response.avisos.map((item: string) => toast.warning(item));
        }
        setIsLoading(false);
        return undefined;
      },
      [handleAdicionarVariacaoCor, temPermissaoCadastrarCor]
    );

    const buscarCores = useCallback(async () => {
      const response = await api.get<void, ResponseApi<OptionTypeBase[]>>(
        ConstanteEnderecoWebservice.COR_LISTAR_SELECT,
        { params: { statusConsulta: StatusConsultaEnum.ATIVOS } }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((item: string) => toast.warning(item));
        }

        if (response.sucesso && response.dados) {
          const cores = response.dados.map((item) => ({
            value: item.id,
            label: item.nome,
            ativo: true,
          }));
          return cores;
        }
      }
      return [];
    }, []);

    const buscarTamanhos = useCallback(async () => {
      const response = await api.get<void, ResponseApi<OptionTypeBase[]>>(
        ConstanteEnderecoWebservice.TAMANHO_LISTAR_SELECT,
        { params: { statusConsulta: StatusConsultaEnum.ATIVOS } }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((item: string) => toast.warning(item));
        }

        if (response.sucesso && response.dados) {
          const cores = response.dados.map((item) => ({
            value: item.id,
            label: item.nome,
            ativo: true,
          }));
          return cores;
        }
      }
      return [];
    }, []);

    const buscarVariacoesDeTamanho = useCallback(
      async (idProduto: string) => {
        const response = await api.get<void, ResponseApi<any[]>>(
          `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${idProduto}/tamanhos`,
          { params: { status: StatusConsultaEnum.ATIVOS } }
        );

        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
          }

          if (response.sucesso) {
            const tamanhos = response.dados
              .filter((item) => item.padraoSistema === false)
              .map((itemTamanho) => ({
                value: itemTamanho.id,
                label: itemTamanho.descricao,
                ativo: itemTamanho.ativo,
              }));
            const tamanhosSistema = await buscarTamanhos();

            if (tamanhosSistema.length > 0 && tamanhos.length > 0) {
              const newTamanhos = tamanhosSistema.filter(
                (tamanho) =>
                  !tamanhos.some(
                    (tamanhoProduto) => tamanhoProduto.value === tamanho.value
                  )
              );

              setTamanhosDoSistema(newTamanhos);
            } else {
              setTamanhosDoSistema(tamanhosSistema);
            }

            return tamanhos;
          }
        }
        return [];
      },
      [buscarTamanhos]
    );

    const buscarVariacoesDeCores = useCallback(
      async (idProduto: string) => {
        const response = await api.get<void, ResponseApi<any[]>>(
          `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${idProduto}/cores`,
          { params: { status: StatusConsultaEnum.ATIVOS } }
        );

        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
            return [];
          }

          if (response.sucesso) {
            const cores = response.dados
              .filter((item) => item?.cor?.padraoSistema === false)
              .map((itemCores) => ({
                value: itemCores.cor.id,
                label: itemCores.cor.descricao,
                ativo: itemCores.ativo,
              }));

            const coresSistema = await buscarCores();

            if (coresSistema.length > 0 && cores.length > 0) {
              const newCores = coresSistema.filter(
                (cor) =>
                  !cores.some((corProduto) => corProduto.value === cor.value)
              );

              setCoresDoSistema(newCores);
            } else {
              setCoresDoSistema(coresSistema);
            }
            return cores;
          }
          return [];
        }
        return [];
      },
      [buscarCores]
    );

    const obterVariacoes = useCallback(async () => {
      setIsLoading(true);
      const tamanhos = await buscarVariacoesDeTamanho(produtoId);
      const cores = await buscarVariacoesDeCores(produtoId);
      setListaCores(cores);
      setListaTamanhos(tamanhos);
      setIsLoading(false);
    }, [buscarVariacoesDeTamanho, buscarVariacoesDeCores, produtoId]);

    const ativarInativarVariacaoCor = useCallback(
      async (idCor: string, ativar: boolean) => {
        const response = await api.patch<void, ResponseApi>(
          `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoId}/cores/${idCor}/ativo/${ativar}`
        );
        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
            return false;
          }

          if (response.sucesso) {
            setListaCores((prevState) => {
              const cor = prevState.find((item) => item.value === idCor);
              const corIndex = prevState.findIndex(
                (item) => item.value === cor?.value
              );
              if (corIndex > -1) {
                prevState[corIndex].ativo = ativar;
              }
              return [...prevState];
            });
            return true;
          }
        }
        return false;
      },
      [produtoId]
    );

    const ativarVariacaoCor = useCallback(
      async (idCor: string) => {
        setIsLoading(true);
        try {
          const success = await ModalAtivarVariacao();
          if (success) {
            const ativado = await ativarInativarVariacaoCor(idCor, true);
            setIsLoading(false);

            return ativado;
          }
        } catch (error) {
          setIsLoading(false);

          return false;
        }
        setIsLoading(false);

        return false;
      },
      [ativarInativarVariacaoCor]
    );

    const ativarInativarVariacaoTamanho = useCallback(
      async (idTamanho: string, ativar: boolean) => {
        const response = await api.patch<void, ResponseApi>(
          `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${produtoId}/tamanhos/${idTamanho}/ativo/${ativar}`
        );
        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
            return false;
          }

          if (response.sucesso) {
            setListaTamanhos((prevState) => {
              const tamanho = prevState?.find(
                (item) => item.value === idTamanho
              );
              const tamanhoIndex = prevState.findIndex(
                (item) => item.value === tamanho?.value
              );
              if (tamanhoIndex > -1) {
                prevState[tamanhoIndex].ativo = ativar;
              }
              return [...prevState];
            });
            return true;
          }
        }
        return false;
      },
      [produtoId]
    );

    const ativarVariacaoTamanho = useCallback(
      async (idTamanho: string) => {
        setIsLoading(true);
        try {
          const success = await ModalAtivarVariacao();
          if (success) {
            const ativado = await ativarInativarVariacaoTamanho(
              idTamanho,
              true
            );
            setIsLoading(false);

            return ativado;
          }
        } catch (error) {
          setIsLoading(false);

          return false;
        }
        setIsLoading(false);

        return false;
      },
      [ativarInativarVariacaoTamanho]
    );

    const confirmarFecharModal = () => {
      onResolve({ sucesso: true, listaCores, listaTamanhos });
      onClose();
    };

    useEffect(() => {
      obterVariacoes();
    }, [obterVariacoes]);

    return (
      <ModalPadraoChakra
        isCentered={!isSmallerThan900}
        size={!isSmallerThan900 ? '3xl' : 'full'}
        {...rest}
        isOpen={isOpen}
        closeOnOverlayClick={false}
        closeOnEsc={false}
        appendToParentPortal={false}
        onClose={onClose}
        scrollBehavior="inside"
      >
        <ModalContent h="unset" bg="gray.50" borderRadius="6px">
          {isLoading && <LoadingPadrao />}
          <ModalHeader
            px="40px"
            py="20px"
            pb="0px"
            color="violet.500"
            fontWeight="700"
            fontSize="16px"
          >
            Cadastrar nova variação
          </ModalHeader>
          <ModalBody px="40px" pt="0px">
            <FormProvider {...formMethods}>
              <Flex
                mt="16px"
                gap="24px"
                flexDirection={['column', 'column', 'row', 'row']}
              >
                <Flex
                  w={['full', 'full', '350px']}
                  py="20px"
                  px="24px"
                  bg="gray.100"
                  border="1px solid #ccc"
                  borderRadius="6px"
                  flexDir="column"
                  gap="24px"
                  h="406px"
                >
                  <Flex gap="2px">
                    <CreatableSelect
                      name="cor"
                      label="Informe a nova COR"
                      fontWeightLabel="bold"
                      placeholder="Digite o nome da cor"
                      creatableInputTextPreffix="Cadastrar a cor"
                      colSpan={[12, 4, 4, 4]}
                      handleCreateOption={handleCadastrarCor}
                      options={coresDoSistema?.sort((a, b) =>
                        a.label.localeCompare(b.label)
                      )}
                      asControlledByObject
                      size="sm"
                      helperText="Pressione Enter para adicionar a cor"
                      onKeyDown={(e) => {
                        const corAntesOnChange = formMethods.watch('cor');
                        if (e.key === 'Enter') {
                          setTimeout(() => {
                            const corAposOnChange = formMethods.watch('cor');
                            if (
                              corAntesOnChange?.value == corAposOnChange?.value
                            ) {
                              handleAdicionarVariacaoCor(corAposOnChange);
                            }
                          }, 0);
                        }
                      }}
                      required
                    />
                    <IconButton
                      _disabled={{
                        bg: 'white',
                        _hover: { bg: 'white', borderColor: 'gray.200' },
                      }}
                      _hover={{ bg: 'white', borderColor: 'gray.300' }}
                      _active={{ bg: 'white' }}
                      aria-label="Adicionar cor"
                      bg="white"
                      mt="18px"
                      borderWidth="1px"
                      borderColor="gray.200"
                      height="32px"
                      minW="32px"
                      borderRadius="md"
                      icon={
                        <FaPlus
                          color={!corEscolhida ? '#BBB' : '#1a8f92'}
                          fontSize="14px"
                        />
                      }
                      isDisabled={!corEscolhida}
                      onClick={() => {
                        const corAposOnChange = formMethods.watch('cor');
                        handleAdicionarVariacaoCor(corAposOnChange);
                      }}
                    />
                  </Flex>
                  {listaCores?.length > 0 ? (
                    <Text fontSize="14px" fontWeight="600" color="gray.700">
                      Variações de COR já cadastradas:
                      <Flex flexDir="column" maxH="260px" overflowY="auto">
                        {listaCores
                          ?.sort((a, b) => a.label.localeCompare(b.label))
                          ?.map((cor) => (
                            <Flex pl="16px" mt="4px">
                              <Flex gap="6px" align="center">
                                <StatusCircle
                                  color={cor.ativo ? 'teal.600' : 'gray.500'}
                                  size="1"
                                />
                                {cor.podeExcluir ? (
                                  <Flex
                                    bg="teal.600"
                                    borderRadius="full"
                                    color="white"
                                    pl="12px"
                                    pr="2px"
                                    align="center"
                                    gap="4px"
                                  >
                                    <Text>{cor.label}</Text>
                                    <IconButton
                                      aria-label=""
                                      padding="2px"
                                      bg="transparent"
                                      onClick={() => {
                                        handleRemoverVariacao(cor, 'cores');
                                      }}
                                      _hover={{ bg: 'white', color: '#1a8f92' }}
                                      size="2"
                                    >
                                      <IoClose />
                                    </IconButton>
                                  </Flex>
                                ) : (
                                  <Text
                                    fontSize="14px"
                                    fontWeight="600"
                                    color={cor.ativo ? 'teal.600' : 'gray.500'}
                                  >
                                    {cor.label}
                                  </Text>
                                )}
                                {!cor.ativo && (
                                  <Text
                                    bg="gray.200"
                                    color="gray.700"
                                    fontWeight="400"
                                    fontSize="12px"
                                    cursor="pointer"
                                    w="64px"
                                    height="18px"
                                    borderRadius="16px"
                                    onClick={() => ativarVariacaoCor(cor.value)}
                                    px="12px"
                                    lineHeight="1.4"
                                  >
                                    Inativa
                                  </Text>
                                )}
                              </Flex>
                            </Flex>
                          ))}
                      </Flex>
                    </Text>
                  ) : (
                    <Text fontSize="14px" fontWeight="600" color="gray.700">
                      Nenhuma cor cadastrada
                    </Text>
                  )}
                </Flex>
                <Flex
                  w={['full', 'full', '350px']}
                  py="20px"
                  px="24px"
                  bg="gray.100"
                  border="1px solid #ccc"
                  borderRadius="6px"
                  flexDir="column"
                  gap="24px"
                  minH="406px"
                >
                  <Flex gap="2px">
                    <CreatableSelect
                      name="tamanho"
                      label="Informe o novo TAMANHO"
                      fontWeightLabel="bold"
                      placeholder="Digite o nome do tamanho"
                      creatableInputTextPreffix="Cadastrar o tamanho"
                      helperText="Pressione Enter para adicionar o tamanho"
                      colSpan={[12, 4, 4, 4]}
                      handleCreateOption={handleCadastrarTamanho}
                      options={tamanhosDoSistema}
                      asControlledByObject
                      size="sm"
                      onKeyDown={(e) => {
                        const tamanhoAntesOnChange =
                          formMethods.watch('tamanho');
                        if (e.key === 'Enter') {
                          setTimeout(() => {
                            const tamanhoAposOnChange =
                              formMethods.watch('tamanho');
                            if (
                              tamanhoAntesOnChange?.value ==
                              tamanhoAposOnChange?.value
                            ) {
                              handleAdicionarVariacaoTamanho(
                                tamanhoAposOnChange
                              );
                            }
                          }, 0);
                        }
                      }}
                      required
                    />
                    <IconButton
                      _disabled={{
                        bg: 'white',
                        _hover: { bg: 'white', borderColor: 'gray.200' },
                      }}
                      _hover={{ bg: 'white', borderColor: 'gray.300' }}
                      _active={{ bg: 'white' }}
                      aria-label="Adicionar tamanho"
                      bg="white"
                      mt="18px"
                      borderWidth="1px"
                      borderColor="gray.200"
                      height="32px"
                      minW="32px"
                      borderRadius="md"
                      icon={
                        <FaPlus
                          color={!tamanhoEscolhido ? '#BBB' : '#97266d'}
                          fontSize="14px"
                        />
                      }
                      isDisabled={!tamanhoEscolhido}
                      onClick={() => {
                        const tamanhoAposOnChange =
                          formMethods.watch('tamanho');
                        handleAdicionarVariacaoTamanho(tamanhoAposOnChange);
                      }}
                    />
                  </Flex>
                  {listaTamanhos?.length > 0 ? (
                    <Text fontSize="14px" fontWeight="600" color="gray.700">
                      Variações de TAMANHO já cadastradas:
                      <Flex flexDir="column" maxH="260px" overflowY="auto">
                        {listaTamanhos?.map((tamanho) => (
                          <Flex pl="16px" mt="4px">
                            <Flex gap="6px" align="center">
                              <StatusCircle
                                color={tamanho.ativo ? 'pink.700' : 'gray.500'}
                                size="1"
                              />
                              {tamanho.podeExcluir ? (
                                <Flex
                                  bg="pink.700"
                                  borderRadius="full"
                                  color="white"
                                  pl="12px"
                                  pr="2px"
                                  align="center"
                                  gap="4px"
                                >
                                  <Text>{tamanho.label}</Text>
                                  <IconButton
                                    aria-label=""
                                    padding="2px"
                                    bg="transparent"
                                    onClick={() => {
                                      handleRemoverVariacao(
                                        tamanho,
                                        'tamanhos'
                                      );
                                    }}
                                    _hover={{ bg: 'white', color: '#97266d' }}
                                    size="2"
                                  >
                                    <IoClose />
                                  </IconButton>
                                </Flex>
                              ) : (
                                <Text
                                  fontSize="14px"
                                  fontWeight="600"
                                  color={
                                    tamanho.ativo ? 'pink.700' : 'gray.500'
                                  }
                                >
                                  {tamanho.label}
                                </Text>
                              )}

                              {!tamanho.ativo && (
                                <Text
                                  bg="gray.200"
                                  color="gray.700"
                                  fontWeight="400"
                                  fontSize="12px"
                                  cursor="pointer"
                                  w="64px"
                                  height="18px"
                                  borderRadius="16px"
                                  px="12px"
                                  lineHeight="1.4"
                                  onClick={() =>
                                    ativarVariacaoTamanho(tamanho.value)
                                  }
                                >
                                  Inativa
                                </Text>
                              )}
                            </Flex>
                          </Flex>
                        ))}
                      </Flex>
                    </Text>
                  ) : (
                    <Text fontSize="14px" fontWeight="600" color="gray.700">
                      Nenhum tamanho cadastrado
                    </Text>
                  )}
                </Flex>
              </Flex>
            </FormProvider>
          </ModalBody>
          <ModalFooter
            p="16px 32px"
            flexDirection={{ base: 'column', md: 'row' }}
            justifyContent="center"
            position="sticky"
            gap="24px"
            borderColor="purple.500"
            mx={{ base: 0, md: 8 }}
            my="24px"
          >
            <Button
              colorScheme="aquamarine"
              variant="solid"
              onClick={() => {
                confirmarFecharModal();
              }}
              borderRadius="full"
              minW="160px"
              height="32px"
              fontSize="14px"
              fontWeight="600"
              w={['full', 'full', '160px']}
            >
              Confirmar
            </Button>
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
